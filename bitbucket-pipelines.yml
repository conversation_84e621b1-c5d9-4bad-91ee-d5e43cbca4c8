image: python:3.12

definitions:
  variables:
    pipeVersion: &pipeVersion fingermarkltd/eyecue-serverless-pipeline:2.0.0

    defaultPipeVariables: &defaultPipeVariables
      NODE_VERSION: v22.14.0
      PYTHON_VERSION: "3.12"
      RUNTIME_LANGUAGE: python

  steps:
    - step: &check-master
        image: atlassian/default-image:4
        name: Check master branch
        script:
          - |
            BRANCH_CURRENT=$(git rev-parse --abbrev-ref HEAD)

            if [[ "${BRANCH_CURRENT}" != "master" ]]; then
              echo "ERROR: Only master branch allowed"
              exit 1
            fi

pipelines:
  # Lint and test on every push
  default:
    - step:
        name: Lint and test
        script:
          - make install-dev
          - make lint
          - mypy --junit-xml="test-results/mypy-results.xml"
          - pytest --junitxml="test-results/test-results.xml"
        caches:
          - pip
        services:
          - docker # Required for localstack
        condition:
          changesets:
            includePaths:
              - "**.py"

  # Deploy to dev when merged with master
  branches:
    master:
      - step: &deploy-dev
          name: Serverless Deploy (dev)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: ${DEV_ACCOUNT_TAG}
                ENVIRONMENT: ${DEV_ENVIRONMENT}
                ORGANIZATION: ${DEV_ORGANIZATION}
                AWS_REGION: ${DEV_AWS_REGION}
                THINGS_SHADOW_TABLE_NAME: ${DEV_THINGS_SHADOW_TABLE_NAME}
                USE_CASES_TABLE_NAME: ${DEV_USE_CASES_TABLE_NAME}
                USE_CASE_INSTALLATIONS_TABLE_NAME: ${DEV_USE_CASE_INSTALLATIONS_TABLE_NAME}
                API_GATEWAY_AUTHORIZER_JWT_DOMAIN: ${DEV_API_GATEWAY_AUTHORIZER_JWT_DOMAIN}
                API_GATEWAY_AUTHORIZER_JWT_AUDIENCE: ${DEV_API_GATEWAY_AUTHORIZER_JWT_AUDIENCE}
                API_GATEWAY_AUTHORIZER_ADMIN_ROLE: ${DEV_API_GATEWAY_AUTHORIZER_ADMIN_ROLE}

  tags:
    # Development tags deploy to dev
    "*[0-9].*[0-9].*[0-9]-*":
      - step: *deploy-dev

    # Production tags deploy to prod
    "*[0-9].*[0-9].*[0-9]":
      - step: *check-master
      - step: &deploy-prod
          name: Serverless Deploy (prod)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: ${PROD_ACCOUNT_TAG}
                ENVIRONMENT: ${PROD_ENVIRONMENT}
                ORGANIZATION: ${PROD_ORGANIZATION}
                AWS_REGION: ${PROD_AWS_REGION}
                THINGS_SHADOW_TABLE_NAME: ${PROD_THINGS_SHADOW_TABLE_NAME}
                USE_CASES_TABLE_NAME: ${PROD_USE_CASES_TABLE_NAME}
                USE_CASE_INSTALLATIONS_TABLE_NAME: ${PROD_USE_CASE_INSTALLATIONS_TABLE_NAME}
                API_GATEWAY_AUTHORIZER_JWT_DOMAIN: ${PROD_API_GATEWAY_AUTHORIZER_JWT_DOMAIN}
                API_GATEWAY_AUTHORIZER_JWT_AUDIENCE: ${PROD_API_GATEWAY_AUTHORIZER_JWT_AUDIENCE}
                API_GATEWAY_AUTHORIZER_ADMIN_ROLE: ${PROD_API_GATEWAY_AUTHORIZER_ADMIN_ROLE}

  custom:
    # Manual deployment options for each environment
    deploy-dev:
      - step: *deploy-dev

    deploy-staging:
      - step:
          name: Serverless Deploy (staging)
          oidc: true
          script:
            - pipe: *pipeVersion
              variables:
                <<: *defaultPipeVariables
                ACCOUNT_TAG: ${STAGING_ACCOUNT_TAG}
                ENVIRONMENT: ${STAGING_ENVIRONMENT}
                ORGANIZATION: ${STAGING_ORGANIZATION}
                AWS_REGION: ${STAGING_AWS_REGION}
                THINGS_SHADOW_TABLE_NAME: ${STAGING_THINGS_SHADOW_TABLE_NAME}
                USE_CASES_TABLE_NAME: ${STAGING_USE_CASES_TABLE_NAME}
                USE_CASE_INSTALLATIONS_TABLE_NAME: ${STAGING_USE_CASE_INSTALLATIONS_TABLE_NAME}
                API_GATEWAY_AUTHORIZER_JWT_DOMAIN: ${STAGING_API_GATEWAY_AUTHORIZER_JWT_DOMAIN}
                API_GATEWAY_AUTHORIZER_JWT_AUDIENCE: ${STAGING_API_GATEWAY_AUTHORIZER_JWT_AUDIENCE}
                API_GATEWAY_AUTHORIZER_ADMIN_ROLE: ${STAGING_API_GATEWAY_AUTHORIZER_ADMIN_ROLE}

    deploy-prod:
      - step: *check-master
      - step: *deploy-prod

    generate-openapi-schema:
      - step:
          name: Auto update OpenAPI schema and Python client
          image: python:3.12
          script:
            - export AWS_DEFAULT_REGION=us-east-1
            - make install-dev
            # Update OpenAPI schema
            - python scripts/dump_openapi_schema.py
            - git add openapi.yaml
            - |
              if git diff --cached --exit-code --quiet; then
                  echo "OpenAPI schema is up to date. No changes to commit."
              else
                  git commit -m "Update OpenAPI schema [skip ci]"
                  git push --atomic
              fi

    bump-version:
      - variables:
          - name: INCREMENT
            default: PATCH
            allowed-values:
              - MAJOR # x.0.0
              - MINOR # 0.x.0
              - PATCH # 0.0.x
      - step: *check-master
      - step:
          name: Bump version
          image: commitizen/commitizen:3.4.0
          script:
            - cz bump --increment $INCREMENT --yes
            - VERSION=$(cz version --project)
            - git push --atomic origin master $VERSION
