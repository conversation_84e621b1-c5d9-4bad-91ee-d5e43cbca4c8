# Development Environment Configuration
ENVIRONMENT=dev
ORGANIZATION=cv-dev
AWS_PROFILE=cv-dev
AWS_REGION=ap-southeast-2

# DynamoDB table names (development)
THINGS_SHADOW_TABLE_NAME=eyecue-things-shadow-dev
USE_CASES_TABLE_NAME=eyecue-use-cases-dev
USE_CASE_INSTALLATIONS_TABLE_NAME=eyecue-use-case-installations-dev

# API Gateway Authorizer configuration (development)
API_GATEWAY_AUTHORIZER_JWT_DOMAIN=https://fingermark.au.auth0.com/
API_GATEWAY_AUTHORIZER_JWT_AUDIENCE=["https://api.dev.eyecue.com", "https://fingermark.au.auth0.com/userinfo"]
API_GATEWAY_AUTHORIZER_ADMIN_ROLE=eyecue-api-things-admin

# Required for serverless-better-credentials
AWS_SDK_LOAD_CONFIG=1
