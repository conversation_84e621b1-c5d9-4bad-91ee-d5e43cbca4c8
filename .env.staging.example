# Staging Environment Configuration
ENVIRONMENT=staging
ORGANIZATION=cv-stg
AWS_PROFILE=cv-stg
AWS_REGION=ap-southeast-2

# DynamoDB table names (staging)
THINGS_SHADOW_TABLE_NAME=eyecue-things-shadow-staging
USE_CASES_TABLE_NAME=eyecue-use-cases-staging
USE_CASE_INSTALLATIONS_TABLE_NAME=eyecue-use-case-installations-staging

# API Gateway Authorizer configuration (staging)
API_GATEWAY_AUTHORIZER_JWT_DOMAIN=https://fingermark.au.auth0.com/
API_GATEWAY_AUTHORIZER_JWT_AUDIENCE=["https://api.staging.eyecue.com", "https://fingermark.au.auth0.com/userinfo"]
API_GATEWAY_AUTHORIZER_ADMIN_ROLE=eyecue-api-things-admin

# Required for serverless-better-credentials
AWS_SDK_LOAD_CONFIG=1
