# Eyecue Things API

This is the AWS Lambda API for the [Eyecue Things](https://bitbucket.org/fingermarkltd/eyecue-things/src/master/) project.

The API is written in Python 3.12 and uses the [Serverless Framework](https://serverless.com/) to deploy to AWS.

You can find the complete documentation and a description of all Lambda functions on this [Notion page](https://www.notion.so/fingermark/Eyecue-Things-API-Documentation-1dec033e5b548009bbbfefe73ea88a43).

## Background

The Eyecue Things project consists of a DynamoDB table called `eyecue-things-shadow` which stores the state of all the things (`eyecue-tracker` & `eyecue-server`) in the fleet. This API is used to interact with this table.

When an item is added/updated in the `eyecue-things-shadow` table, there is a lambda which is triggered to update the state of the Thing, which is subsequently reloaded in either the `eyecue-tracker` or `eyecue-server` on the server.

## Getting Started

First, clone the repository:

```bash
git clone https://bitbucket.org/fingermarkltd/eyecue-things-api.git
cd eyecue-things-api
```

Then, create a virtual environment and install the dependencies:

```bash
# With virtualenv
python3 -m venv venv
source venv/bin/activate

# With pyenv
pyenv virtualenv 3.12 eyecue-things-api
pyenv activate eyecue-things-api

# Install dependencies
make install-dev
```

To run the tests:

```bash
make test
# or
pytest
# or, a specific test
pytest -k name_of_my_test
```

> ⚠️ **Note**: The tests use localstack so make sure you have Docker installed and running.

## Deployment

The pipeline supports three environments: **dev** (`cv-dev`), **staging** (`cv-stg`), and **production** (`cv-prod`).

> ⚠️ **Important**: Before using the pipeline, you must configure repository variables in Bitbucket. See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed configuration instructions.

### Option 1: Manually via Bitbucket Pipelines

You can deploy this project via the Bitbucket pipelines:

- **Development**: Go to `Pipelines` ► `Run pipeline` ► Select your `Branch` and the `Pipeline` called `deploy-dev` ► `Run`
- **Staging**: Go to `Pipelines` ► `Run pipeline` ► Select your `Branch` and the `Pipeline` called `deploy-staging` ► `Run`
- **Production**: Go to `Pipelines` ► `Run pipeline` ► Select `master` branch and the `Pipeline` called `deploy-prod` ► `Run`

### Option 2: Via pushing a tag

- **Development Deployment**: Push a tag in the format `1.0.0-anything` (e.g., `1.0.0-beta`, `1.0.0-rc1`) to deploy to the dev environment
- **Production Deployment**: Push a tag in the format `1.0.0` (semantic version) to deploy to production. Note that this will only work from the `master` branch.

### Option 3: Automatic deployment

- **Development**: Automatically deploys when code is merged to the `master` branch

### Option 4: Via Serverless locally

You can deploy to any environment by running the following commands:

> ⚠️ **Note**: This will only work if you are updating the stack, it will not work if you are
> creating it for the first time as the PowerAccess role does not have the necessary permissions.

```bash
# Copy the appropriate environment file
cp .env.dev.example .env      # For development
cp .env.staging.example .env  # For staging
cp .env.prod.example .env     # For production

# You will need to assume PowerAccess role in the target account
# Update the AWS_PROFILE in your .env file or assume the role manually:
source <(fm aws assume-role cv-dev --role PowerAccess)    # For dev
source <(fm aws assume-role cv-stg --role PowerAccess)    # For staging
source <(fm aws assume-role cv-prod --role PowerAccess)   # For production

# Install the serverless framework and the dependencies
npm install

# Deploy the API
npm run deploy
```

For detailed configuration and troubleshooting, see [DEPLOYMENT.md](./DEPLOYMENT.md).
