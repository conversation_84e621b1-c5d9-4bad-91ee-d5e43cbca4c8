# Deployment Configuration

This document describes how to configure the Bitbucket pipeline for deploying to different environments (dev, staging, production).

## Overview

The pipeline now supports three environments:
- **Development (dev)**: Deploys to `cv-dev` account
- **Staging (staging)**: Deploys to `cv-stg` account  
- **Production (prod)**: Deploys to `cv-prod` account

## Repository Variables Configuration

You need to configure the following repository variables in Bitbucket under **Repository Settings > Pipelines > Repository variables**:

### Development Environment Variables

| Variable Name | Example Value | Description |
|---------------|---------------|-------------|
| `DEV_ACCOUNT_TAG` | `cv-dev` | AWS account tag for development |
| `DEV_ENVIRONMENT` | `dev` | Environment name |
| `DEV_ORGANIZATION` | `cv-dev` | Organization identifier |
| `DEV_AWS_REGION` | `ap-southeast-2` | AWS region |
| `DEV_THINGS_SHADOW_TABLE_NAME` | `eyecue-things-shadow-dev` | DynamoDB table name for things shadow |
| `DEV_USE_CASES_TABLE_NAME` | `eyecue-use-cases-dev` | DynamoDB table name for use cases |
| `DEV_USE_CASE_INSTALLATIONS_TABLE_NAME` | `eyecue-use-case-installations-dev` | DynamoDB table name for installations |
| `DEV_API_GATEWAY_AUTHORIZER_JWT_DOMAIN` | `https://fingermark.au.auth0.com/` | JWT domain for API Gateway authorizer |
| `DEV_API_GATEWAY_AUTHORIZER_JWT_AUDIENCE` | `["https://api.dev.eyecue.com"]` | JWT audience for API Gateway authorizer |
| `DEV_API_GATEWAY_AUTHORIZER_ADMIN_ROLE` | `eyecue-api-things-admin` | Admin role for API Gateway authorizer |

### Staging Environment Variables

| Variable Name | Example Value | Description |
|---------------|---------------|-------------|
| `STAGING_ACCOUNT_TAG` | `cv-stg` | AWS account tag for staging |
| `STAGING_ENVIRONMENT` | `staging` | Environment name |
| `STAGING_ORGANIZATION` | `cv-stg` | Organization identifier |
| `STAGING_AWS_REGION` | `ap-southeast-2` | AWS region |
| `STAGING_THINGS_SHADOW_TABLE_NAME` | `eyecue-things-shadow-staging` | DynamoDB table name for things shadow |
| `STAGING_USE_CASES_TABLE_NAME` | `eyecue-use-cases-staging` | DynamoDB table name for use cases |
| `STAGING_USE_CASE_INSTALLATIONS_TABLE_NAME` | `eyecue-use-case-installations-staging` | DynamoDB table name for installations |
| `STAGING_API_GATEWAY_AUTHORIZER_JWT_DOMAIN` | `https://fingermark.au.auth0.com/` | JWT domain for API Gateway authorizer |
| `STAGING_API_GATEWAY_AUTHORIZER_JWT_AUDIENCE` | `["https://api.staging.eyecue.com"]` | JWT audience for API Gateway authorizer |
| `STAGING_API_GATEWAY_AUTHORIZER_ADMIN_ROLE` | `eyecue-api-things-admin` | Admin role for API Gateway authorizer |

### Production Environment Variables

| Variable Name | Example Value | Description |
|---------------|---------------|-------------|
| `PROD_ACCOUNT_TAG` | `cv-prod` | AWS account tag for production |
| `PROD_ENVIRONMENT` | `prod` | Environment name |
| `PROD_ORGANIZATION` | `cv-prod` | Organization identifier |
| `PROD_AWS_REGION` | `ap-southeast-2` | AWS region |
| `PROD_THINGS_SHADOW_TABLE_NAME` | `eyecue-things-shadow` | DynamoDB table name for things shadow |
| `PROD_USE_CASES_TABLE_NAME` | `eyecue-use-cases` | DynamoDB table name for use cases |
| `PROD_USE_CASE_INSTALLATIONS_TABLE_NAME` | `eyecue-use-case-installations` | DynamoDB table name for installations |
| `PROD_API_GATEWAY_AUTHORIZER_JWT_DOMAIN` | `https://fingermark.au.auth0.com/` | JWT domain for API Gateway authorizer |
| `PROD_API_GATEWAY_AUTHORIZER_JWT_AUDIENCE` | `["https://api.eyecue.com"]` | JWT audience for API Gateway authorizer |
| `PROD_API_GATEWAY_AUTHORIZER_ADMIN_ROLE` | `eyecue-api-things-admin` | Admin role for API Gateway authorizer |

## Deployment Triggers

### Automatic Deployments

1. **Development**: Automatically deploys when code is merged to `master` branch
2. **Development Tags**: Tags in format `1.0.0-anything` (e.g., `1.0.0-beta`, `1.0.0-rc1`) deploy to development
3. **Production Tags**: Tags in format `1.0.0` (semantic version) deploy to production (master branch only)

### Manual Deployments

You can manually trigger deployments using custom pipelines:

1. **Deploy to Dev**: Use the `deploy-dev` custom pipeline
2. **Deploy to Staging**: Use the `deploy-staging` custom pipeline  
3. **Deploy to Prod**: Use the `deploy-prod` custom pipeline (master branch only)

## Environment-Specific Configuration

The serverless.yml file will automatically pick up the environment variables passed from the pipeline. The key variables that affect the deployment are:

- `ENVIRONMENT`: Sets the serverless stage and stack tags
- `ORGANIZATION`: Used in stack tags for customer identification
- `AWS_REGION`: Determines the AWS region for deployment
- Table names: Configure DynamoDB table names per environment
- JWT configuration: Environment-specific Auth0 settings

## Security Considerations

1. **Secured Variables**: Mark sensitive variables (like JWT secrets) as "Secured" in Bitbucket
2. **OIDC Authentication**: The pipeline uses OIDC for AWS authentication
3. **Branch Protection**: Production deployments are restricted to the master branch
4. **Account Isolation**: Each environment deploys to a separate AWS account

## Migration from Old Pipeline

The old pipeline used hardcoded account tags:
- `qa-aus` → Now configurable via `DEV_ACCOUNT_TAG`
- `cv-prod` → Now configurable via `PROD_ACCOUNT_TAG`
- `dev-nzl`, `stg-nzl` → Replaced with configurable staging environment

Update your repository variables according to the tables above to maintain the same deployment targets or configure new ones as needed.
