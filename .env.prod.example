# Production Environment Configuration
ENVIRONMENT=prod
ORGANIZATION=cv-prod
AWS_PROFILE=cv-prod
AWS_REGION=ap-southeast-2

# DynamoDB table names (production)
THINGS_SHADOW_TABLE_NAME=eyecue-things-shadow
USE_CASES_TABLE_NAME=eyecue-use-cases
USE_CASE_INSTALLATIONS_TABLE_NAME=eyecue-use-case-installations

# API Gateway Authorizer configuration (production)
API_GATEWAY_AUTHORIZER_JWT_DOMAIN=https://fingermark.au.auth0.com/
API_GATEWAY_AUTHORIZER_JWT_AUDIENCE=["https://api.eyecue.com", "https://fingermark.au.auth0.com/userinfo"]
API_GATEWAY_AUTHORIZER_ADMIN_ROLE=eyecue-api-things-admin

# Required for serverless-better-credentials
AWS_SDK_LOAD_CONFIG=1
